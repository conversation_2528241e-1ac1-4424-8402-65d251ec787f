<template>
  <div class="kanban">
    <van-nav-bar title="看板" left-arrow @click-left="$router.back()" />
    
    <div class="kanban-content">
      <van-tabs v-model:active="activeTab">
        <van-tab title="待办事项">
          <div class="tab-content">
            <van-cell-group>
              <van-cell title="完成项目报告" label="截止时间：今天 18:00" />
              <van-cell title="参加团队会议" label="截止时间：明天 10:00" />
              <van-cell title="代码审查" label="截止时间：本周五" />
            </van-cell-group>
          </div>
        </van-tab>
        
        <van-tab title="进行中">
          <div class="tab-content">
            <van-cell-group>
              <van-cell title="开发新功能" label="进度：60%" />
              <van-cell title="系统测试" label="进度：30%" />
            </van-cell-group>
          </div>
        </van-tab>
        
        <van-tab title="已完成">
          <div class="tab-content">
            <van-cell-group>
              <van-cell title="需求分析" label="完成时间：昨天" />
              <van-cell title="UI设计" label="完成时间：上周" />
            </van-cell-group>
          </div>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const activeTab = ref(0)
</script>

<style scoped>
.kanban {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.kanban-content {
  padding-top: 0;
}

.tab-content {
  padding: 16px;
}
</style>
