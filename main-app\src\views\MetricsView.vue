<template>
  <div class="metrics">
    <van-nav-bar title="指标库" left-arrow @click-left="$router.back()" />
    
    <div class="metrics-content">
      <van-search v-model="searchValue" placeholder="搜索指标" />
      
      <div class="metrics-categories">
        <van-grid :column-num="2" :border="false" :gutter="12">
          <van-grid-item v-for="category in categories" :key="category.name" @click="selectCategory(category)">
            <div class="category-card" :class="category.color">
              <van-icon :name="category.icon" size="24" />
              <span class="category-name">{{ category.name }}</span>
              <span class="category-count">{{ category.count }}个指标</span>
            </div>
          </van-grid-item>
        </van-grid>
      </div>
      
      <van-cell-group title="热门指标">
        <van-cell v-for="metric in popularMetrics" :key="metric.name" 
                  :title="metric.name" 
                  :label="metric.description" 
                  is-link 
                  @click="viewMetric(metric)" />
      </van-cell-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const searchValue = ref('')

const categories = ref([
  { name: '业务指标', icon: 'chart-trending-o', count: 25, color: 'blue' },
  { name: '技术指标', icon: 'setting-o', count: 18, color: 'green' },
  { name: '用户指标', icon: 'friends-o', count: 12, color: 'orange' },
  { name: '财务指标', icon: 'gold-coin-o', count: 8, color: 'purple' }
])

const popularMetrics = ref([
  { name: '日活跃用户数', description: '每日活跃用户统计' },
  { name: '系统响应时间', description: '平均系统响应时间' },
  { name: '转化率', description: '用户转化率统计' },
  { name: '收入增长率', description: '月度收入增长率' }
])

const selectCategory = (category: any) => {
  console.log('选择分类:', category.name)
}

const viewMetric = (metric: any) => {
  console.log('查看指标:', metric.name)
}
</script>

<style scoped>
.metrics {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.metrics-content {
  padding: 16px;
}

.metrics-categories {
  margin: 16px 0;
}

.category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100px;
  border-radius: 8px;
  color: white;
  text-align: center;
  padding: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.category-card.blue {
  background: linear-gradient(135deg, #1989fa 0%, #4fc3f7 100%);
}

.category-card.green {
  background: linear-gradient(135deg, #07c160 0%, #4caf50 100%);
}

.category-card.orange {
  background: linear-gradient(135deg, #ff976a 0%, #ffa726 100%);
}

.category-card.purple {
  background: linear-gradient(135deg, #7c4dff 0%, #9c27b0 100%);
}

.category-name {
  margin-top: 8px;
  font-size: 14px;
  font-weight: 600;
}

.category-count {
  margin-top: 4px;
  font-size: 12px;
  opacity: 0.8;
}
</style>
