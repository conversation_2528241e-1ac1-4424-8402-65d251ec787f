<template>
  <div class="dashboard">
    <van-nav-bar title="驾驶舱" left-arrow @click-left="$router.back()" />
    
    <div class="dashboard-content">
      <van-cell-group title="数据概览">
        <van-cell title="今日访问量" value="1,234" />
        <van-cell title="本月活跃用户" value="5,678" />
        <van-cell title="系统运行状态" value="正常" />
      </van-cell-group>
      
      <div class="chart-section">
        <van-cell-group title="数据图表">
          <div class="chart-placeholder">
            <van-icon name="chart-trending-o" size="48" color="#1989fa" />
            <p>图表数据加载中...</p>
          </div>
        </van-cell-group>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 驾驶舱页面逻辑
</script>

<style scoped>
.dashboard {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.dashboard-content {
  padding: 16px;
}

.chart-section {
  margin-top: 16px;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: white;
  border-radius: 8px;
  margin: 16px;
}

.chart-placeholder p {
  margin-top: 12px;
  color: #969799;
  font-size: 14px;
}
</style>
